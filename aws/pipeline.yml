resource_types:
- name: paas-image
  type: docker-image
  source:
    repository: ((dtr_images_repo))
    ca_certs:
    - domain: ((dtr_repo_domain))
      cert: ((dtr_repo_cert))
    username: ((paas-eo-username))
    password: ((paas-eo-password))
    tag: latest

resources:
- name: terraform-state
  type: s3
  source:
    disable_ssl: false
    access_key_id: ((aws_access_key_id))
    secret_access_key: ((aws_secret_access_key))
    endpoint: {{S3_ENDPOINT}}
    bucket: {{S3_OUTPUT_BUCKET}}
    region_name: {{aws_region}}
    versioned_file: terraform.tfstate

- name: install-hybrid-paas-repo
  type: git
  source:
    uri: https://gitlab.global.lmco.com/hybrid-cloud/paas/hybrid-paas-pipeline
    branch: master
    skip_ssl_verification: true
    username: gitlab+deploy-token-2
    password: ********************

jobs:
- name: bootstrap-terraform-state
  serial_groups: [terraform]
  plan:
  - get: install-hybrid-paas-repo
  - task: create-terraform-state
    params:
      TERRAFORM_PREFIX: {{terraform_prefix}}
      S3_BUCKET_TERRAFORM: {{S3_OUTPUT_BUCKET}}
      S3_ENDPOINT: {{S3_ENDPOINT}}
      S3_REGION: {{aws_region}}
      AWS_ACCESS_KEY_ID: ((aws_access_key_id))
      AWS_SECRET_ACCESS_KEY: ((aws_secret_access_key))
    file: install-hybrid-paas-repo/aws/tasks/create-initial-terraform-state/task.yml

- name: create-infrastructure
  serial_groups: [terraform]
  plan:
  - aggregate:
    - get: install-hybrid-paas-repo
      trigger: false
    - get: terraform-state
      # passed: [bootstrap-terraform-state]
  - task: create-infrastructure
    file: install-hybrid-paas-repo/aws/tasks/create-infrastructure/task.yml
    params:
      TERRAFORM_PREFIX: {{terraform_prefix}}
      aws_access_key_id: ((aws_access_key_id))
      aws_secret_access_key: ((aws_secret_access_key))
      aws_region: {{aws_region}}
      aws_key_name: {{aws_key_name}}
      bastion_ami: {{bastion_ami}}
      amis_nat: {{amis_nat}}
      iam_region: {{iam_region}}
      s3_region: {{s3_region}}
      common_deny_arn: {{common_deny_arn}}
      aws_az1: {{aws_az1}}
      aws_az2: {{aws_az2}}
      # aws_az3: {{aws_az3}}
      LMI_subnet_az1_id: {{LMI_subnet_az1_id}}
      LMI_subnet_az2_id: {{LMI_subnet_az2_id}}
      # LMI_subnet_az3_id: {{LMI_subnet_az3_id}}
      vpc_id: {{vpc_id}}
      vpc_cidr: {{vpc_cidr}}
      network_acl_id: {{network_acl_id}}
      private_subnet_cidr_az1: {{private_subnet_cidr_az1}}
      private_subnet_cidr_az2: {{private_subnet_cidr_az2}}
      # private_subnet_cidr_az3: {{private_subnet_cidr_az3}}
      bastion_ip_az1: {{bastion_ip_az1}}
      nat_ip_az1: {{nat_ip_az1}}
      nat_ip_az2: {{nat_ip_az2}}
      # nat_ip_az3: {{nat_ip_az3}}
    ensure:
      put: terraform-state
      params:
        file: terraform-state-output/terraform.tfstate

- name: deploy-openshift
  serial_groups: [openshift]
  plan:
  - aggregate:
    - get: install-hybrid-paas-repo
    - get: terraform-state
      trigger: false
      passed: [create-infrastructure]
  - task: deploy-openshift
    file: install-hybrid-paas-repo/aws/tasks/openshift-install/task.yml
    params:
      REGION: {{aws_region}}
      ENV: {{env}}
      TERRAFORM_PREFIX: {{terraform_prefix}}
      MASTER_INSTANCE_TYPE: {{master_instance_type}}
      CLUSTER_NAME: {{cluster_name}}
      DOMAIN_NAME: {{domain_name}}

- name: wipe-env
  serial_groups: [terraform]
  ensure:
    put: terraform-state
    params:
      file: terraform-state/terraform.tfstate
  plan:
  - aggregate:
    - get: install-hybrid-paas-repo
    - get: terraform-state
  - task: wipe
    file: install-hybrid-paas-repo/aws/tasks/wipe-env/task.yml
    params:
      aws_access_key_id: ((aws_access_key_id))
      aws_secret_access_key: ((aws_secret_access_key))
      aws_region: {{aws_region}}
      TERRAFORM_PREFIX: {{terraform_prefix}}
      aws_key_name: {{aws_key_name}}
      bastion_ami: {{bastion_ami}}
      amis_nat: {{amis_nat}}
      iam_region: {{iam_region}}
      common_deny_arn: {{common_deny_arn}}
      aws_az1: {{aws_az1}}
      aws_az2: {{aws_az2}}
      # aws_az3: {{aws_az3}}
      LMI_subnet_az1_id: {{LMI_subnet_az1_id}}
      LMI_subnet_az2_id: {{LMI_subnet_az2_id}}
      # LMI_subnet_az3_id: {{LMI_subnet_az3_id}}
      vpc_id: {{vpc_id}}
      vpc_cidr: {{vpc_cidr}}
      network_acl_id: {{network_acl_id}}
      private_subnet_cidr_az1: {{private_subnet_cidr_az1}}
      private_subnet_cidr_az2: {{private_subnet_cidr_az2}}
      # private_subnet_cidr_az3: {{private_subnet_cidr_az3}}
      bastion_ip_az1: {{bastion_ip_az1}}
      nat_ip_az1: {{nat_ip_az1}}
      nat_ip_az2: {{nat_ip_az2}}
      # nat_ip_az3: {{nat_ip_az3}}