# For terraform state file (http://docs.aws.amazon.com/general/latest/gr/rande.html#s3_region)
#S3_ENDPOINT: https://s3-fips.us-east-1.amazonaws.com
S3_ENDPOINT: https://s3.us-west-1.amazonaws.com
S3_OUTPUT_BUCKET: hp-upw1-dev-pipeline
aws_region: us-west-1
env: dev
terraform_prefix: hp-upw1-dev
iam_region: aws # <-- aws-us-gov or aws
s3_region: "aws:s3"
common_deny_arn: arn:aws:iam::740359841657:policy/CommonDeny

# AMI to use for nat instances
amis_nat: ami-01cd9be80fcd58eb1
# AMI to use for bastion host
bastion_ami: ami-0d821453063a3c9b1
aws_key_name: ocp-hpw1-dev
bastion_ip_az1: ************* # <--- enter bastion IP, use the base IP +10
nat_ip_az1: ************* # <--- enter NatAz1 IP use the Base IP +20
nat_ip_az2: ************** # <--- enter NatAz2 IP use the base IP +20
nat_ip_az3: "" # <--- enter NatAz3 IP

vpc_id: vpc-0359ce462d6a1c56c
vpc_cidr: *************/26
network_acl_id: acl-0dce7c09ea398aa6b
aws_az1: us-west-1a
aws_az2: us-west-1c
aws_az3: ""
LMI_subnet_az1_id: subnet-0c0185ad7e691975b
LMI_subnet_az2_id: subnet-07f69ec3c82f2b650
LMI_subnet_az3_id:
private_subnet_cidr_az1: **********/20
private_subnet_cidr_az2: ***********/20
private_subnet_cidr_az3: ""

master_instance_type: m5.xlarge
cluster_name: upw1-dev
domain_name: global.lmco.com
