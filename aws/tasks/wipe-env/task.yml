---
platform: linux
image_resource:
  type: docker-image
  source:
    repository: ((dtr_images_repo))
    ca_certs:
    - domain: ((dtr_repo_domain))
      cert: ((dtr_repo_cert))
    username: ((paas-eo-username))
    password: ((paas-eo-password))
    tag: latest

inputs:
  - name: install-hybrid-paas-repo
  - name: terraform-state
outputs:
  - name: terraform-state-output
params:
  aws_access_key_id:
  aws_secret_access_key:
  aws_region:
  TERRAFORM_PREFIX:
  aws_key_name:
  aws_cert_arn:
  bastion_ami:
  amis_nat:
  iam_region:
  s3_region:
  common_deny_arn:
  aws_az1:
  aws_az2:
  #aws_az3:
  LMI_subnet_az1_id:
  LMI_subnet_az2_id:
  #LMI_subnet_az3_id:
  vpc_id:
  vpc_cidr:
  network_acl_id:
  private_subnet_cidr_az1:
  private_subnet_cidr_az2:
  #private_subnet_cidr_az3:
  bastion_ip_az1:
  nat_ip_az1:
  nat_ip_az2:
  #nat_ip_az3:
run:
  path: install-hybrid-paas-repo/aws/tasks/wipe-env/task.sh
