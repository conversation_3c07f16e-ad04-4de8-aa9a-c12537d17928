#!/bin/bash
set -eu

root=$(pwd)

aws configure set default.region $aws_region
aws configure set aws_access_key_id $aws_access_key_id
aws configure set aws_secret_access_key $aws_secret_access_key

cd install-hybrid-paas-repo/aws/tasks/create-infrastructure/terraform

aws_vpc_id=$(jq -r '.modules[0].outputs.vpc_id.value' $root/terraform-state/terraform.tfstate)

terraform init

terraform destroy \
  -force \
  -var "prefix=dontcare" \
  -var "aws_access_key_id=${aws_access_key_id}" \
  -var "aws_secret_access_key=${aws_secret_access_key}" \
  -var "aws_region=${aws_region}" \
  -var "vpc_id=dontcare" \
  -var "aws_key_name=dontcare" \
  -var "aws_cert_arn=arn:a:a:aa-a-1:012345678912:a" \
  -var "amis_nat=dontcare" \
  -var "iam_region=dontcare" \
  -var "s3_region=dontcare" \
  -var "common_deny_arn=dontcare" \
  -var "aws_az1=dontcare" \
  -var "aws_az2=dontcare" \
  -var "private_subnet_cidr_az1=**********/20" \
  -var "private_subnet_cidr_az2=***********/20" \
  -var "vpc_cidr=**********/16" \
  -var "bastion_ip_az1=dontcare" \
  -var "bastion_ami=dontcare" \
  -var "nat_ip_az1=dontcare" \
  -var "nat_ip_az2=dontcare" \
  -var "LMI_subnet_az1_id=0.0.0.0/0" \
  -var "LMI_subnet_az2_id=0.0.0.0/0" \
  -var "network_acl_id=dontcare" \
  -state "${root}/terraform-state/terraform.tfstate" \
  -state-out "${root}/terraform-state-output/terraform.tfstate"

openshift_cidr_association=$(aws ec2 describe-vpcs --region us-west-1 | jq '[ .Vpcs[].CidrBlockAssociationSet[] | select((.CidrBlock == "**********/16") and .CidrBlockState.State == "associated") | .AssociationId]' | grep \" | cut -d '"' -f2)

if [[ -z "$openshift_cidr_association" ]]; then
  echo -e "private cidr does not exist"
else
  aws ec2 disassociate-vpc-cidr-block --association-id $openshift_cidr_association --region $aws_region
fi
