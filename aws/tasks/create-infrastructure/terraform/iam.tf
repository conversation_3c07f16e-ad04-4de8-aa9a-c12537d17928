//master iam role, profile, policy
resource "aws_kms_key" "s3-kms-key" {
  description             = "${var.prefix}-s3-kms-key"
  deletion_window_in_days = 30
}
resource "aws_kms_alias" "s3-kms-key-alias" {
  name          = "alias/${var.prefix}-s3-kms-key"
  target_key_id = "${aws_kms_key.s3-kms-key.key_id}"
}
resource "aws_iam_role" "master_role" {
    name = "${var.prefix}-_master_role"
    assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "ec2.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": "MasterRolePolicy"
    }
  ]
}
EOF
}
resource "aws_iam_role_policy_attachment" "master_policy_role_attach" {
    role = "${aws_iam_role.master_role.name}"
    policy_arn = "${aws_iam_policy.masterpolicy.arn}"
}
resource "aws_iam_role_policy_attachment" "common_deny_master_policy_role_attach" {
    role = "${aws_iam_role.master_role.name}"
    policy_arn = "${var.common_deny_arn}"
}
resource "aws_iam_instance_profile" "master_role_instance_profile" {
    name = "${var.prefix}_master_role_instance_profile"
    role = "${aws_iam_role.master_role.name}"
}
resource "aws_iam_policy" "masterpolicy" {
    name = "${var.prefix}_masterpolicy"
    path = "/"
    description = "${var.prefix} openshift master policy"
    policy = "${data.aws_iam_policy_document.master_iam_role_policy_document.json}"
}

# Policy Document
data "aws_iam_policy_document" "master_iam_role_policy_document" {
    policy_id = "${var.prefix}_MasterIamRolePolicyDocument"
    statement {
    sid = "1"

    actions = [
      "ec2:*",
      "elasticloadbalancing:*",
      "s3:GetObject",
      "iam:PassRole",
    ]

    resources = [
      "*",
    ]
  }

  statement {
    actions = [
      "iam:GetInstanceProfile",
    ]

    resources = [
      "${aws_iam_instance_profile.master_role_instance_profile.arn}",
    ]
  }
}

//worker iam role, profile, policy
resource "aws_iam_role" "worker_role" {
    name = "${var.prefix}_worker_role"
    assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "ec2.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": "openshiftWorkerRolePolicy"
    }
  ]
}
EOF
}
resource "aws_iam_role_policy_attachment" "worker_policy_role_attach" {
    role = "${aws_iam_role.worker_role.name}"
    policy_arn = "${aws_iam_policy.workerpolicy.arn}"
}
resource "aws_iam_role_policy_attachment" "common_deny_worker_policy_role_attach" {
    role = "${aws_iam_role.worker_role.name}"
    policy_arn = "${var.common_deny_arn}"
}
resource "aws_iam_instance_profile" "worker_role_instance_profile" {
    name = "${var.prefix}_worker_role_instance_profile"
    role = "${aws_iam_role.worker_role.name}"
}
resource "aws_iam_policy" "workerpolicy" {
    name = "${var.prefix}_workerpolicy"
    path = "/"
    description = "${var.prefix} openshift worker policy"
    policy = "${data.aws_iam_policy_document.worker_iam_role_policy_document.json}"
}

# Policy Document
data "aws_iam_policy_document" "worker_iam_role_policy_document" {
    policy_id = "${var.prefix}_WorkerIamRolePolicyDocument"
    statement {
    sid = "1"
    actions = [
      "ec2:Describe*",
    ]
    resources = [
        "*",
    ]
  }
}
//Bootstrap iam role, profile, policy
resource "aws_iam_role" "bootstrap_role" {
    name = "${var.prefix}_bootstrap_role"
    assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "ec2.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": "openshiftbootstrapRolePolicy"
    }
  ]
}
EOF
}
resource "aws_iam_role_policy_attachment" "bootstrap_policy_role_attach" {
    role = "${aws_iam_role.bootstrap_role.name}"
    policy_arn = "${aws_iam_policy.bootstrappolicy.arn}"
}
resource "aws_iam_role_policy_attachment" "common_deny_bootstrap_policy_role_attach" {
    role = "${aws_iam_role.bootstrap_role.name}"
    policy_arn = "${var.common_deny_arn}"
}
resource "aws_iam_instance_profile" "bootstrap_role_instance_profile" {
    name = "${var.prefix}_bootstrap_role_instance_profile"
    role = "${aws_iam_role.bootstrap_role.name}"
}
resource "aws_iam_policy" "bootstrappolicy" {
    name = "${var.prefix}_bootstrappolicy"
    path = "/"
    description = "${var.prefix} openshift bootstrap policy"
    policy = "${data.aws_iam_policy_document.bootstrap_iam_role_policy_document.json}"
}

# Policy Document
data "aws_iam_policy_document" "bootstrap_iam_role_policy_document" {
    policy_id = "${var.prefix}_BootstrapIamRolePolicyDocument"
    statement {
      sid = "1"
      actions = [
        "ec2:Describe*",
        "ec2:AttachVolume",
        "ec2:DetachVolume",
      ]
      resources = [
          "*",
      ]
    }
}