resource "aws_route_table_association" "nat_instance_1_route_association" {
  subnet_id      = "${aws_subnet.OpenShiftPrivateSubnet_az1.id}"
  route_table_id = "${aws_route_table.PrivateSubnetRouteTable_az1.id}"
}

resource "aws_route_table_association" "nat_instance_2_route_association" {
  subnet_id      = "${aws_subnet.OpenShiftPrivateSubnet_az2.id}"
  route_table_id = "${aws_route_table.PrivateSubnetRouteTable_az2.id}"
}

/*resource "aws_route_table_association" "nat_instance_3_route_association" {
  subnet_id      = "${aws_subnet.OpenShiftPrivateSubnet_az3.id}"
  route_table_id = "${aws_route_table.PrivateSubnetRouteTable_az3.id}"
}
*/