//  Output some useful variables for quick SSH access etc.
output "prefix" {
    value = "${var.prefix}"
}
output "region" {
    value = "${var.aws_region}"
}
output "iam_region" {
    value = "${var.iam_region}"
}
output "s3_region" {
    value = "${var.s3_region}"
}
output "az1" {
    value = "${var.aws_az1}"
}
output "az2" {
    value = "${var.aws_az2}"
}
/*
output "az3" {
    value = "${var.aws_az3}"
}
*/
output "vpc_id" {
    value = "${var.vpc_id}"
}
output "vpc_cidr" {
    value = "${var.vpc_cidr}"
}
output "OpenShiftPrivateSubnet_az1" {
    value = "${aws_subnet.OpenShiftPrivateSubnet_az1.id}"
}
output "OpenShiftPrivateSubnet_az2" {
    value = "${aws_subnet.OpenShiftPrivateSubnet_az2.id}"
}
output "master_sg" {
    value = "${aws_security_group.master_sg.id}"
}
output "worker_sg" {
    value = "${aws_security_group.worker_sg.id}"
}
output "bastion_host_sg" {
    value = "${aws_security_group.bastion_host_sg.id}"
}
output "bastion_identifier" {
    value = "${aws_instance.bastion.tags.Name}"
}
output "bastion-private_dns" {
  value = "${aws_instance.bastion.private_dns}"
}
output "bastion-private_ip" {
  value = "${aws_instance.bastion.private_ip}"
}
output "nat_az1-private_dns" {
  value = "${aws_instance.nat_az1.private_dns}"
}
output "nat_az1-private_ip" {
  value = "${aws_instance.nat_az1.private_ip}"
}
output "nat_az2-private_dns" {
  value = "${aws_instance.nat_az2.private_dns}"
}
output "nat_az2-private_ip" {
  value = "${aws_instance.nat_az2.private_ip}"
}
output "external-lb" {
  value = "${aws_lb.external-lb.arn}"
}
output "ext_lb_listener" {
  value = "${aws_lb_listener.ext_front_end.arn}"
}
output "ext_lb_target_group" {
  value = "${aws_lb_target_group.ext_target_group.arn}"
}
output "internal-lb" {
  value = "${aws_lb.internal-lb.arn}"
}
output "int_api_lb_listener" {
  value = "${aws_lb_listener.int_api_front_end.arn}"
}
output "int_api_lb_target_group" {
  value = "${aws_lb_target_group.int_api_target_group.arn}"
}
output "int_svc_lb_listener" {
  value = "${aws_lb_listener.int_svc_front_end.arn}"
}
output "int_svc_lb_target_group" {
  value = "${aws_lb_target_group.int_svc_target_group.arn}"
}
output "master-instance-profile-arn" {
  value = "${aws_iam_instance_profile.master_role_instance_profile.arn}"
}
output "worker-instance-profile-arn" {
  value = "${aws_iam_instance_profile.worker_role_instance_profile.arn}"
}
output "bootstrap-instance-profile-arn" {
  value = "${aws_iam_instance_profile.bootstrap_role_instance_profile.arn}"
}
output "s3-kms-key" {
  value = "${aws_kms_key.s3-kms-key.arn}"
}
output "ocp-backup-bucket" {
  value = "${aws_s3_bucket.ocp-backup.id}"
}
output "ocp-registry-bucket" {
  value = "${aws_s3_bucket.ocp-registry.id}"
}
output "ocp-deploy-files-bucket" {
  value = "${aws_s3_bucket.ocp-deploy-files.id}"
}