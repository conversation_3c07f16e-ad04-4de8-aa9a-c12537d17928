resource "aws_security_group" "all_nodes_sg" {
  name = "${var.prefix}-all_nodes_sg"
  description = "All Nodes security group."
  vpc_id = "${var.vpc_id}"
  tags = {
        Name = "${var.prefix}-all_nodes_sg"
  }
# Ingress Rules
  ingress {
      from_port = 22
      to_port = 22
      protocol = "tcp"
      cidr_blocks = ["***********/32"]
      description = "LM ssh ips"
  }
  ingress {
      from_port = 22
      to_port = 22
      protocol = "tcp"
      cidr_blocks = ["**************/26"]
      description = "Concourse Tools SSH"
  }
  ingress {
      from_port = 22
      to_port = 22
      protocol = "tcp"
      #cidr_blocks = ["0.0.0.0/0"]
      self = true
      description = "SSH for ansible hosts into instances-nodes"
  }
  ingress {
      from_port = 53
      to_port = 53
      protocol = "tcp"
      self = true
      description = ""
  }
  ingress {
      from_port = 80
      to_port = 80
      protocol = "tcp"
      self = true
      description = "HTTP communication between nodes"
  }
  ingress {
      from_port = 443
      to_port = 443
      protocol = "tcp"
      self = true
      description = "Internal Master WebUI API"
  }

  ingress {
      from_port = 443
      to_port = 443
      protocol = "tcp"
      cidr_blocks = ["***********/27"]
      description = "Gitlab.us.lmco.com Registry.gitlab.us.lmco.com access"
  }
  ingress {
      from_port = 3269
      to_port = 3269
      protocol = "tcp"
      cidr_blocks = ["*************/32"]
      description = "LDAP for usadc50.us.lmco.com server"
  }
  ingress {
      from_port = 4789
      to_port = 4789
      protocol = "udp"
      self = true
      description = "SDN pod to pod communications"
  }
  ingress {
      from_port = 6443
      to_port = 6443
      protocol = "tcp"
      self = true
      description = "6443/healthz/ready controller-manager"
  }
  ingress {
      from_port = 8053
      to_port = 8053
      protocol = "udp"
      self = true
      description = "Internal Name Services UDP"
  }
  ingress {
      from_port = 8053
      to_port = 8053
      protocol = "tcp"
      self = true
      description = "Internal Name Services TCP"
  }
  ingress {
      from_port = 8080
      to_port = 8080
      protocol = "tcp"
      self = true
      description = "HTTP Communications between nodes"
  }
  ingress {
      from_port = 8443
      to_port = 8443
      protocol = "tcp"
      self = true
      description = "HTTP Communications between nodes"
  }
  ingress {
      from_port = 9100
      to_port = 9100
      protocol = "tcp"
      self = true
      description = "Monitoring Prometheus node_exporter service listener"
  }
  ingress {
      from_port =10250
      to_port = 10250
      protocol = "tcp"
      self = true
      description = "Kubelet Communications"
  }
  ingress {
      from_port =10250
      to_port = 10250
      protocol = "tcp"
      cidr_blocks = ["0.0.0.0/0"]
      description = "AMQ port"
  }
# Egress Rules
  egress {
      from_port = 0
      to_port = 0
      protocol = "-1"
      cidr_blocks = ["0.0.0.0/0"]
  }

}
resource "aws_security_group" "master_sg" {
  name = "${var.prefix}-master_sg"
  description = "Cluster Master Security Group"
  vpc_id = "${var.vpc_id}"
  tags = {
        Name = "${var.prefix}-master_sg"
  }
# Egress Rules
  ingress {
      from_port = 0
      to_port = 0
      protocol = "icmp"
      cidr_blocks = ["${var.vpc_cidr}"]
      description = "icmp"
  }
  ingress {
      from_port = 22
      to_port = 22
      protocol = "tcp"
      cidr_blocks = ["${var.vpc_cidr}"]
      description = "ssh"
  }
  ingress {
      from_port = 6443
      to_port = 6443
      protocol = "tcp"
      cidr_blocks = ["${var.vpc_cidr}"]
      description = "Kubernetes API"
  }
  ingress {
      from_port = 22623
      to_port = 22623
      protocol = "tcp"
      cidr_blocks = ["${var.vpc_cidr}"]
      description = "Machine Config server"
  }
  ingress {
      from_port = 443
      to_port = 443
      protocol = "tcp"
      cidr_blocks = ["*************/32"]
      description = "Ping Federation SSO UAT env"
  }
  ingress {
      from_port = 443
      to_port = 443
      protocol = "tcp"
      cidr_blocks = ["**************/32"]
      description = "Ping Federation SSO test env"
  }
  ingress {
      from_port = 443
      to_port = 443
      protocol = "tcp"
      cidr_blocks = ["*************/32","*************/32"]
      description = "Ping Federation SSO prod  env"
  }
  ingress {
      from_port = 2379
      to_port = 2380
      protocol = "tcp"
      self = true
      description = "Etcd Node Health Port"
  }
  egress {
      from_port = 0
      to_port = 0
      protocol = "-1"
      cidr_blocks = ["0.0.0.0/0"]
  }
  ingress {
      from_port = 4789
      to_port = 4789
      protocol = "udp"
      self = true
      description = "VXLAN and GENEVE packets"
  }
  ingress {
      from_port = 9000
      to_port = 9999
      protocol = "tcp"
      self = true
      description = "Internal cluster communication"
  }
  ingress {
      from_port = 10250
      to_port = 10259
      protocol = "tcp"
      self = true
      description = "Kubernetes kubelet, scheduler and controller manager"
  }
  ingress {
      from_port = 30000
      to_port = 32767
      protocol = "tcp"
      self = true
      description = "Kubernetes ingress services"
  }

}
/*these rules depend on both security groups (master and worker sgs) so separating it allows them
to be created after as to avoid a circular reference*/
resource "aws_security_group_rule" "master_sg_extra_rule1" {
  security_group_id        = "${aws_security_group.master_sg.id}"
  from_port                = 4789
  to_port                  = 4789
  protocol                 = "udp"
  type                     = "ingress"
  source_security_group_id = "${aws_security_group.worker_sg.id}"
  description              = "VXLAN and GENEVE packets"
}
resource "aws_security_group_rule" "master_sg_extra_rule2" {
  security_group_id        = "${aws_security_group.master_sg.id}"
  from_port                = 9000
  to_port                  = 9999
  protocol                 = "tcp"
  type                     = "ingress"
  source_security_group_id = "${aws_security_group.worker_sg.id}"
  description              = "Internal cluster communication"
}
resource "aws_security_group_rule" "master_sg_extra_rule3" {
  security_group_id        = "${aws_security_group.master_sg.id}"
  from_port                = 10250
  to_port                  = 10259
  protocol                 = "tcp"
  type                     = "ingress"
  source_security_group_id = "${aws_security_group.worker_sg.id}"
  description              = "Kubernetes kubelet, scheduler and controller manager"
}
resource "aws_security_group_rule" "master_sg_extra_rule4" {
  security_group_id        = "${aws_security_group.master_sg.id}"
  from_port                = 30000
  to_port                  = 32767
  protocol                 = "tcp"
  type                     = "ingress"
  source_security_group_id = "${aws_security_group.worker_sg.id}"
  description              = "Kubernetes ingress services"
}
resource "aws_security_group" "worker_sg" {
  name = "${var.prefix}-worker_sg"
  description = "Cluster Worker Security Group"
  vpc_id = "${var.vpc_id}"
  tags = {
      Name = "${var.prefix}-worker_sg"
  }
# Ingress Rules
  ingress {
      from_port = 0
      to_port = 0
      protocol = "icmp"
      cidr_blocks = ["${var.vpc_cidr}"]
      description = "icmp"
  }
  ingress {
      from_port = 22
      to_port = 22
      protocol = "tcp"
      cidr_blocks = ["${var.vpc_cidr}"]
      description = "ssh"
  }
  ingress {
      from_port = 4789
      to_port = 4789
      protocol = "udp"
      self = true
      security_groups = ["${aws_security_group.master_sg.id}"]
      description = "VXLAN and GENEVE packets"
  }
  ingress {
      from_port = 9000
      to_port = 9999
      protocol = "tcp"
      self = true
      security_groups = ["${aws_security_group.master_sg.id}"]
      description = "Internal cluster communication"
  }
  ingress {
      from_port = 10250
      to_port = 10250
      protocol = "tcp"
      self = true
      security_groups = ["${aws_security_group.master_sg.id}"]
      description = "Kubernetes secure kubelet port and Internal Kubernetes Communication"
  }
  ingress {
      from_port = 30000
      to_port = 32767
      protocol = "tcp"
      self = true
      security_groups = ["${aws_security_group.master_sg.id}"]
      description = "Kubernetes ingress services"
  }
# Egress Rules
  egress {
      from_port = 0
      to_port = 0
      protocol = "-1"
      cidr_blocks = ["0.0.0.0/0"]
  }

}
resource "aws_security_group" "nat_instance_sg" {
  name = "${var.prefix}-nat_instance_sg"
  description = "NAT Instance security group."
  vpc_id = "${var.vpc_id}"
  tags = {
      Name = "${var.prefix}-nat_instance_sg"
  }
# Ingress Rules
  ingress {
      from_port = 0
      to_port = 0
      protocol = "-1"
      cidr_blocks = ["**********/16"]
      description = "Private OCP Subnets"
  }
  # Egress Rules
  egress {
      from_port = 0
      to_port = 0
      protocol = "-1"
      cidr_blocks = ["0.0.0.0/0"]
  }

}
resource "aws_security_group" "bastion_host_sg" {
  name = "${var.prefix}-bastion_host_sg"
  description = "Bastion Host security group."
  vpc_id = "${var.vpc_id}"
  tags = {
      Name = "${var.prefix}-bastion_host_sg"
  }
# Ingress Rules
  ingress {
      from_port = 0
      to_port = 0
      protocol = "-1"
      cidr_blocks = ["**************/26"] # Update with Tools VPC CIDR
      description = "Concourse Tools VPC"
  }
  # Egress Rules
    egress {
      from_port = 0
      to_port = 0
      protocol = "-1"
      cidr_blocks = ["0.0.0.0/0"]
  }

}
