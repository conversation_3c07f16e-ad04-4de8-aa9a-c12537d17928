resource "aws_route_table" "PrivateSubnetRouteTable_az1" {
  vpc_id = "${var.vpc_id}"

  route {
    cidr_block = "0.0.0.0/0"
    instance_id = "${aws_instance.nat_az1.id}"
  }

  tags = {
    Name = "${var.prefix}-PrivateSubnetRouteTable_az1"
  }
}
resource "aws_route_table" "PrivateSubnetRouteTable_az2" {
  vpc_id = "${var.vpc_id}"

  route {
    cidr_block = "0.0.0.0/0"
    instance_id = "${aws_instance.nat_az2.id}"
  }

  tags = {
    Name = "${var.prefix}-PrivateSubnetRouteTable_az2"
  }
}

/*resource "aws_route_table" "PrivateSubnetRouteTable_az3" {
  vpc_id = "${var.vpc_id}"

  route {
    cidr_block = "0.0.0.0/0"
    instance_id = "${aws_instance.nat_az3.id}"
  }

  tags = {
    Name = "${var.prefix}-PrivateSubnetRouteTable_az3"
  }
}
*/