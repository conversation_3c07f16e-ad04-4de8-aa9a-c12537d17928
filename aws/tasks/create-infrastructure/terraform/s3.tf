resource "aws_s3_bucket" "log-bucket" {
    bucket = "${var.prefix}-s3-logs"
    acl    = "log-delivery-write"
    force_destroy= true
    server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        kms_master_key_id = "${aws_kms_key.s3-kms-key.arn}"
        sse_algorithm     = "aws:kms"
      }
    }
  }
    tags = {
        Name = "${var.prefix}-s3-logs"
        Environment = "${var.prefix}"
    }
}

resource "aws_s3_bucket" "ocp-backup" {
    bucket = "${var.prefix}-backup"
    acl = "private"
    force_destroy= true
    server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        kms_master_key_id = "${aws_kms_key.s3-kms-key.arn}"
        sse_algorithm     = "aws:kms"
      }
    }
  }
    logging {
        target_bucket = "${aws_s3_bucket.log-bucket.id}"
        target_prefix = "log/"
    }
    tags = {
        Name = "${var.prefix}-backup"
        Environment = "${var.prefix}"
    }
}
resource "aws_s3_bucket" "ocp-registry" {
    bucket = "${var.prefix}-registry"
    acl = "private"
    force_destroy= true
    server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        kms_master_key_id = "${aws_kms_key.s3-kms-key.arn}"
        sse_algorithm     = "aws:kms"
      }
    }
  }
    logging {
        target_bucket = "${aws_s3_bucket.log-bucket.id}"
        target_prefix = "log/"
    }
    tags = {
        Name = "${var.prefix}-registry"
        Environment = "${var.prefix}"
    }
}
resource "aws_s3_bucket" "ocp-deploy-files" {
    bucket = "${var.prefix}-ocp-deploy-files"
    acl = "private"
    force_destroy= true
    server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        kms_master_key_id = "${aws_kms_key.s3-kms-key.arn}"
        sse_algorithm     = "aws:kms"
      }
    }
  }
    logging {
        target_bucket = "${aws_s3_bucket.log-bucket.id}"
        target_prefix = "log/"
    }
    tags = {
        Name = "${var.prefix}-ocp-deploy-files"
        Environment = "${var.prefix}"
    }
}