# Create a new load balancer
resource "aws_lb" "external-lb" {
  name               = "${var.prefix}-ext-lb"
  internal           = true
  load_balancer_type = "network"
  subnets            = ["${var.LMI_subnet_az1_id}","${var.LMI_subnet_az2_id}"]

  enable_deletion_protection = false

  tags = {
    Environment = "${var.prefix}-ext-lb"
  }
}
//listner for infra LB
resource "aws_lb_listener" "ext_front_end" {
  load_balancer_arn = "${aws_lb.external-lb.arn}"
  port              = "6443"
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = "${aws_lb_target_group.ext_target_group.arn}"
  }
}

//IP target Group
resource "aws_lb_target_group" "ext_target_group" {
  name        = "${var.prefix}-ext-lb-tg"
  port        = 6443
  protocol    = "TCP"
  target_type = "ip"
  vpc_id      = "${var.vpc_id}"
  stickiness{
    enabled = false
    type = "lb_cookie"
  }
}


resource "aws_lb" "internal-lb" {
  name               = "${var.prefix}-int-lb"
  internal           = true
  load_balancer_type = "network"
  subnets            = ["${aws_subnet.OpenShiftPrivateSubnet_az1.id}","${aws_subnet.OpenShiftPrivateSubnet_az2.id}"]

  enable_deletion_protection = false

  tags = {
    Environment = "${var.prefix}-int-lb"
  }
}
//listner for API internal LB
resource "aws_lb_listener" "int_api_front_end" {
  load_balancer_arn = "${aws_lb.internal-lb.arn}"
  port              = "6443"
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = "${aws_lb_target_group.int_api_target_group.arn}"
  }
}

//IP API target Group
resource "aws_lb_target_group" "int_api_target_group" {
  name        = "${var.prefix}-int-api-lb-tg"
  port        = 6443
  protocol    = "TCP"
  target_type = "ip"
  vpc_id      = "${var.vpc_id}"
  stickiness{
    enabled = false
    type = "lb_cookie"
  }
}
//listner for Service internal LB
resource "aws_lb_listener" "int_svc_front_end" {
  load_balancer_arn = "${aws_lb.internal-lb.arn}"
  port              = "22623"
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = "${aws_lb_target_group.int_svc_target_group.arn}"
  }
}

//IP Service target Group
resource "aws_lb_target_group" "int_svc_target_group" {
  name        = "${var.prefix}-int-svc-lb-tg"
  port        = 22623
  protocol    = "TCP"
  target_type = "ip"
  vpc_id      = "${var.vpc_id}"
  stickiness{
    enabled = false
    type = "lb_cookie"
  }
}

//target group attachment
/*resource "aws_lb_target_group_attachment" "infra_lb_group_attachment" {
  target_group_arn = "${aws_lb_target_group.infra_lb_target_group.arn}"
  target_id        = "${aws_instance.bastion.private_ip}"
  port             = 443
}*/


/*resource "aws_lb" "ocp-infra-public" {
  name = "${var.prefix}-infra-public-lb"
  load_balancer_type = "network"
  //cross_zone_load_balancing   = true
  security_groups = ["${aws_security_group.lb_sg.id}","${aws_security_group.infra_sg.id}","${aws_security_group.all_nodes_sg.id}"]
  subnets = ["${var.LMI_subnet_az1_id}","${var.LMI_subnet_az2_id}"]
  idle_timeout                = 400
  internal = true

  listener {
    instance_port      = 80
    instance_protocol  = "http"
    lb_port            = 80
    lb_protocol        = "http"
  }

  listener {
    instance_port      = 443
    instance_protocol  = "http"
    lb_port            = 443
    lb_protocol        = "http"
  }

  health_check {
    healthy_threshold   = 10
    unhealthy_threshold = 2
    timeout             = 5
    target              = "TCP:443"
    interval            = 30
  }

  tags = {
    Name = "ocp-infra-public-lb"
  }
}

# Create a new load balancer
resource "aws_lb" "ocp-master-internal" {
  name = "${var.prefix}-master-internal-lb"
  load_balancer_type = "network"
  //cross_zone_load_balancing   = true
  security_groups = ["${aws_security_group.all_nodes_sg.id}","${aws_security_group.master_sg.id}"]
  subnets = ["${var.LMI_subnet_az1_id}","${var.LMI_subnet_az2_id}"]
  idle_timeout                = 400
  internal = true

  listener {
    instance_port      = 443
    instance_protocol  = "tcp"
    lb_port            = 443
    lb_protocol        = "tcp"
  }

  listener {
    instance_port      = 8443
    instance_protocol  = "tcp"
    lb_port            = 8443
    lb_protocol        = "tcp"
  }

  health_check {
    healthy_threshold   = 10
    unhealthy_threshold = 2
    timeout             = 5
    target              = "TCP:8443"
    interval            = 30
  }

  tags = {
    Name = "${var.prefix}-master-internal-lb"
  }
}


# Create a new load balancer
resource "aws_lb" "ocp-master-public" {
  name = "${var.prefix}-master-public-lb"
  load_balancer_type = "network"
  //cross_zone_load_balancing   = true
  security_groups = ["${aws_security_group.lb_sg.id}","${aws_security_group.all_nodes_sg.id}","${aws_security_group.master_sg.id}"]
  subnets = ["${var.LMI_subnet_az1_id}","${var.LMI_subnet_az2_id}"]
  idle_timeout                = 400
  internal = true

  listener {
    instance_port      = 8443
    instance_protocol  = "tcp"
    lb_port            = 8443
    lb_protocol        = "tcp"
  }

  health_check {
    healthy_threshold   = 10
    unhealthy_threshold = 2
    timeout             = 5
    target              = "TCP:8443"
    interval            = 30
  }

  tags = {
    Name = "${var.prefix}-master-public-lb"
  }
}*/
