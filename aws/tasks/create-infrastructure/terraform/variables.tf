variable "common_deny_arn" {}
variable "aws_access_key_id" {}
variable "aws_secret_access_key" {}
variable "aws_key_name" {}
variable "aws_cert_arn" {}
variable "s3_region" {}
variable "iam_region" {}
variable "prefix" {}
variable "bastion_ami" {}
variable "amis_nat" {}
variable "aws_region" {}
variable "aws_az1" {}
variable "aws_az2" {}
#variable "aws_az3" {}
variable "LMI_subnet_az1_id" {}
variable "LMI_subnet_az2_id" {}
#variable "LMI_subnet_az3_id" {}
variable "vpc_id" {}
variable "network_acl_id" {}

variable "bastion_instance_type" {
  description = "Instance Type for Bastion Host"
  default     = "m4.large"
}
variable "nat_instance_type" {
  description = "Instance Type for NAT instances"
  default     = "t2.medium"
}

variable "vpc_cidr" {
  description = "CIDR for the whole VPC"
  default     = "10.0.0.0/16"
}
/*
  Availability Zone 1
*/
variable "private_subnet_cidr_az1" {
  description = "CIDR for the Private Subnet 1"
  default     = "**********/20"
}
variable "nat_ip_az1" {
  default = "********"
}
variable "bastion_ip_az1" {
  default = "********"
}

/*
  Availability Zone 2
*/
variable "private_subnet_cidr_az2" {
  description = "CIDR for the Private Subnet 2"
  default     = "***********/20"
}
variable "nat_ip_az2" {
  default = "********"
}

/*
  Availability Zone 3
*/
/*
variable "private_subnet_cidr_az3" {
    description = "CIDR for the Public Subnet 3"
    default = "***********/20"
}

variable "nat_ip_az3" {
    default = "********"
}
*/
