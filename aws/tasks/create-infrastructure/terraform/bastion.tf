resource "aws_network_interface" "bastion_private_ip" {
  subnet_id   = "${aws_subnet.OpenShiftPrivateSubnet_az1.id}"
  security_groups = ["${aws_security_group.all_nodes_sg.id}"]
  attachment {
      instance     = "${aws_instance.bastion.id}"
      device_index = 1
    }
  }

# Create Bastion Host instance
resource "aws_instance" "bastion" {
  ami = "${var.bastion_ami}"
  availability_zone = "${var.aws_az1}"
  instance_type = "t2.small"
  key_name = "${var.aws_key_name}"
  vpc_security_group_ids = ["${aws_security_group.all_nodes_sg.id}","${aws_security_group.bastion_host_sg.id}"]
  subnet_id = "${var.LMI_subnet_az1_id}"
  associate_public_ip_address = false
  private_ip = "${var.bastion_ip_az1}"
  root_block_device {
      volume_size = "150"
      volume_type = "gp2"
  }
  tags = {
      Name = "${var.prefix}-Bastion-Host-az1"
  }
}