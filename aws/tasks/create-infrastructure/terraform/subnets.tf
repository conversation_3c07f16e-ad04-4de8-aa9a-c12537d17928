/*
  For First availability zone
*/

# 1. Create Private Subnet 1
# 1.1 Non-routable AZ1
resource "aws_subnet" "OpenShiftPrivateSubnet_az1" {
    vpc_id = "${var.vpc_id}"

    cidr_block = "${var.private_subnet_cidr_az1}"
    availability_zone = "${var.aws_az1}"

    tags = {
        Name = "${var.prefix}-OpenShift Private Subnet AZ1"
    }
}

# 2. Create Private Subnet 2
# 2.1 Non-routable AZ2
resource "aws_subnet" "OpenShiftPrivateSubnet_az2" {
    vpc_id = "${var.vpc_id}"

    cidr_block = "${var.private_subnet_cidr_az2}"
    availability_zone = "${var.aws_az2}"

    tags = {
        Name = "${var.prefix}-OpenShift Private Subnet AZ2"
    }
}

/*
  For Second availability zone. There will not be modification to main routing table as it was already
  done while setting up
*/

# 3. Create Private Subnet 3
# 3.1 Non-routable AZ3
/*resource "aws_subnet" "OpenShiftPrivateSubnet_az3" {
    vpc_id = "${var.vpc_id}"

    cidr_block = "${var.private_subnet_cidr_az3}"
    availability_zone = "${var.aws_az3}"

    tags = {
        Name = "${var.prefix}-OpenShift Private Subnet AZ3"
    }
}*/

/*
  For Third availability zone.  There will not be modification to main routing table as it was already
  done while setting up
  */