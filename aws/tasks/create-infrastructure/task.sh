#!/bin/bash

set -eu

INIT_DIR=$(pwd)

aws configure set default.region $aws_region
aws configure set aws_access_key_id $aws_access_key_id
aws configure set aws_secret_access_key $aws_secret_access_key

#os_cidr=$(aws ec2 describe-vpcs --vpc-ids $vpc_id | grep '100.64.0.0/16' | sed 's/^ *//g')
os_cidr=$(aws ec2 describe-vpcs --vpc-ids $vpc_id | jq -r '[ .Vpcs[].CidrBlockAssociationSet[] | select((.CidrBlock == "100.64.0.0/16") and .CidrBlockState.State == "associated") | .AssociationId]' | sed 's/\[//g; s/\]//g')

if [[ -z "$os_cidr" ]]; then
  echo -e "associating private network cidr"
  aws ec2 associate-vpc-cidr-block --vpc-id $vpc_id --cidr-block 100.64.0.0/16 --region $aws_region
else
  echo -e "private cidr exists"
fi

cd install-hybrid-paas-repo/aws/tasks/create-infrastructure/terraform

terraform init

terraform plan -compact-warnings \
  -state "$INIT_DIR"/terraform-state/terraform.tfstate \
  -var "prefix=${TERRAFORM_PREFIX}" \
  -var "aws_access_key_id=${aws_access_key_id}" \
  -var "aws_secret_access_key=${aws_secret_access_key}" \
  -var "aws_key_name=${aws_key_name}" \
  -var "aws_cert_arn=${aws_cert_arn}" \
  -var "bastion_ami=${bastion_ami}" \
  -var "amis_nat=${amis_nat}" \
  -var "aws_region=${aws_region}" \
  -var "iam_region=${iam_region}" \
  -var "s3_region=${s3_region}" \
  -var "common_deny_arn=${common_deny_arn}" \
  -var "aws_az1=${aws_az1}" \
  -var "aws_az2=${aws_az2}" \
  -var "vpc_cidr=${vpc_cidr}" \
  -var "private_subnet_cidr_az1=${private_subnet_cidr_az1}" \
  -var "private_subnet_cidr_az2=${private_subnet_cidr_az2}" \
  -var "bastion_ip_az1=${bastion_ip_az1}" \
  -var "nat_ip_az1=${nat_ip_az1}" \
  -var "nat_ip_az2=${nat_ip_az2}" \
  -var "LMI_subnet_az1_id=${LMI_subnet_az1_id}" \
  -var "LMI_subnet_az2_id=${LMI_subnet_az2_id}" \
  -var "vpc_id=${vpc_id}" \
  -var "network_acl_id=${network_acl_id}" \
  -out "$INIT_DIR"/terraform.tfplan

terraform apply -compact-warnings \
  -state-out "$INIT_DIR"/terraform-state-output/terraform.tfstate \
  "$INIT_DIR"/terraform.tfplan
