data "aws_ami" "rhcos" {
  most_recent = true

  filter {
    name   = "name"
    values = ["rhcos-43.81.2020*-hvm"]
  }

  filter {
    name   = "root-device-type"
    values = ["ebs"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }

  owners = ["531415883065"] # Canonical
}

data "aws_region" "current" {}

data "aws_subnet_ids" "private" {
  vpc_id = "${var.vpc_id}"

  tags = {
    Name = "*Private*"
  }
}

resource "aws_instance" "master-node" {
  count         = "3"
  ami           = "${data.aws_ami.rhcos.id}"
  instance_type = "m5.xlarge"
  key_name      = "${var.aws_key_name}"
  subnet_id     = "${element(tolist(data.aws_subnet_ids.private.ids), count.index)}"
  volume_size   = "120"
  volume_type   = "gp2"
  #user_data_base64 = "${base64encode(local.master-userdata)}"
  tags = {
    Name        = "Master-${count.index}"
    Environment = "${var.prefix}"
  }
}
