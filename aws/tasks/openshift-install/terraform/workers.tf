#############################################################################################
#                                                                                           #
# Title:        ec2-worker.tf                                                               #
# Version:                                                                                  #
#               2020-05-11 WRC. Initial                                                     #
# Create Date:  2020-05-11                                                                  #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                          #
# Description:  Initializes data for the terraform configuration                            #
#                                                                                           #
#############################################################################################

#-----------------------#
# Variables Section     #
#-----------------------#
#variable "EnvironmentType" {
#    type        = string
#    description = "The OpenShift allowed environment types."
#    default     = "tst"
#}

#variable "ignition_location" {
#    type        = string
#    default     =   "https://api-int.oc.global.lmco.com:22623/config/worker"
#   description = "Ignition config file location."
#}

#variable "instances" {
#    type        = list(number)
#    description = "Instances."
#    default     = "[0,1,2]"
#}

#-----------------------#
# Local Variables       #
#-----------------------#
locals {
  node_type = "worker"
}

#############################################################################################
# Resource Section                                                                          #
#############################################################################################

#-------------------------------#
# Resource: bootstrap_policy    #
#-------------------------------#
resource "aws_iam_role_policy" "WorkerIamPolicy" {
  name   = "${var.prefix}-WorkerIamPolicy"
  role   = aws_iam_role.WorkerIamRole.id
  policy = <<-EOF
    {
      "Version": "2012-10-17",
      "Statement": [
        {
          "Action": [
            "ec2:Describe*",
            "ec2:AttachVolume",
            "ec2:DetachVolume",
            "s3:GetObject"
          ],
          "Effect": "Allow",
          "Resource": "*"
        }
      ]
    }
    EOF
}

#-------------------------------#
# Resource: bootstrap_role      #
#-------------------------------#
resource "aws_iam_role" "WorkerIamRole" {
  name                  = "${var.prefix}-WorkerIamRole"
  description           = "${var.prefix}-WorkerIamRole"
  path                  = "/"
  force_detach_policies = false
  assume_role_policy    = <<-EOF
    {
        "Version": "2012-10-17",
        "Statement": [
        {
            "Sid": "",
            "Effect": "Allow",
            "Action": "sts:AssumeRole",
            "Principal": { "Service": "ec2.amazonaws.com" }
        }
        ]
    }
    EOF
  tags = {
    EnvironmentType = var.EnvironmentType
    NodeType        = local.node_type
  }
}

#-------------------------------#
# Resource: instance_profile    #
#-------------------------------#
resource "aws_iam_instance_profile" "WorkerInstanceProfile" {
  name = "${var.prefix}-WorkerInstanceProfile"
  role = aws_iam_role.WorkerIamRole.name
}

#---------------------------------------#
# Resource: bootstrap security group    #
#---------------------------------------#
resource "aws_security_group" "WorkerSecurityGroup" {
  name        = "${var.prefix}-WorkerSecurityGroup"
  description = "${var.prefix}-Worker Host security Group."
  vpc_id      = var.vpc_id
  tags = {
    Name            = "${var.prefix}-WorkerSecurityGroup"
    EnvironmentType = var.EnvironmentType
    NodeType        = local.node_type
  }

  ingress {
    description = "LM ssh ips"
    cidr_blocks = ["***********/32"]
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
  }

  ingress {
    description = "TLS from VPC"
    from_port   = 19531
    to_port     = 19531
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}


#---------------------------------------#
# Resource: worker instances            #
#---------------------------------------#
resource "aws_instance" "WorkerInstance" {
  count                       = 1
  ami                         = data.aws_ami.rhcos.id
  associate_public_ip_address = false
  #   availability_zone               = var.aws_region
  disable_api_termination = false
  ebs_optimized           = true
  iam_instance_profile    = aws_iam_instance_profile.WorkerInstanceProfile.id
  instance_type           = "m5.xlarge"
  ipv6_address_count      = 0
  key_name                = "ocp-hpw1-tst"
  monitoring              = true
  #   private_ip
  source_dest_check = true
  subnet_id         = var.private_subnet_cidr_az1

  root_block_device {
    volume_type           = "gp2"
    volume_size           = "120"
    delete_on_termination = true
    encrypted             = true
  }

  user_data = <<-EOF
        {"ignition":{"config":{"replace":{"source":"${var.BootstrapIgnitionLocation}","verification":{}}},"timeouts":{},"version":"2.1.0"},"networkd":{},"passwd":{},"storage":{},"systemd":{}}
    EOF

  tags = {
    # kubernetes.io/cluster/test = "shared"
    # !Join ["", ["kubernetes.io/cluster/", !Ref InfrastructureName]]
    Name            = format("%s-worker-%02d", var.prefix, count.index)
    Prefix          = var.prefix
    EnvironmentType = var.EnvironmentType
    NodeType        = local.node_type
    NodeNumber      = count.index
    RegionCode      = "upw1"
  }
}
