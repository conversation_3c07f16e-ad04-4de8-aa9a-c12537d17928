#!/usr/bin/bash
export ClusterName="devocp"
export HostedZoneName="sharkbait.tech"
export VpcCidr="**********/19"
export RhcosAmi="ami-046fe691f52a953f9"

mkdir $ClusterName.$HostedZoneName
cd $ClusterName.$HostedZoneName
cp ../install-config.yaml .
sed -i s/myBaseDomain/$HostedZoneName/ install-config.yaml
sed -i s/myClusterName/$ClusterName/ install-config.yaml
sed -i '3,12d' install-config.yaml
sed -i '/baseDomain/a\
compute:\
- hyperthreading: Enabled\
  name: worker\
  platform:\
    aws:\
      zones:\
      - us-east-1a\
      #- us-east-1b\
      #- us-east-1c\
      type: m4.xlarge\
  replicas: 0\
controlPlane:\
  hyperthreading: Enabled\
  name: master\
  platform:\
      aws:\
      zones:\
      - us-east-1a\
      #- us-east-1b\
      #- us-east-1c\
      type: m4.xlarge\
  replicas: 3' install-config.yaml
echo "####################install-config.yaml#################"      
cat install-config.yaml
cp install-config.yaml install-config.bak.post
../openshift-install create manifests --dir=.
# Ignore the warning about compute nodes - we create them later.
rm -f openshift/99_openshift-cluster-api_master-machines-*.yaml
rm -f openshift/99_openshift-cluster-api_worker-machineset-*
../openshift-install create ignition-configs --dir=.
export InfrastructureName=`jq -r .infraID metadata.json`

`aws route53 list-hosted-zones-by-name | jq --arg name "$HostedZoneName." -r '.HostedZones | .[] | select(.Name=="\($name)") | .Id' | awk -F/ '{print "export HostedZoneId="$3}'`

echo '
[
  {
    "ParameterKey": "VpcCidr",
    "ParameterValue": "'$VpcCidr'"
  },
  {
    "ParameterKey": "AvailabilityZoneCount",
    "ParameterValue": "1"
  },
  {
    "ParameterKey": "SubnetBits",
    "ParameterValue": "5"
  }
]' > parameter-vpc.json
echo "####################parameter-vpc.json#################"      
cat parameter-vpc.json
cp ../cf.vpc.yaml .

echo "################Creating VPC Stack##################"
aws cloudformation create-stack --stack-name $ClusterName-vpc --template-body file://cf.vpc.yaml --parameters file://parameter-vpc.json --capabilities CAPABILITY_NAMED_IAM

while [ -n "$(aws cloudformation describe-stacks --stack-name $ClusterName-vpc | awk '/CREATE_IN_PROGRESS/ {print $2}')" ]; do echo; date; \
aws cloudformation describe-stacks --stack-name $ClusterName-vpc | awk '/CREATE_IN_PROGRESS/ {print $2}'; sleep 10; done;

`aws cloudformation describe-stacks --stack-name $ClusterName-vpc | jq  -r '.Stacks[].Outputs[]  | "\(.OutputKey) \(.OutputValue)"' | awk '{print "export "$1"="$2}'`

echo "############################"
echo "VpcID: $VpcId"
echo "PublicSubnetIds: $PublicSubnetIds"
echo "PrivateSubnetIds: $PrivateSubnetIds"
echo "HostedZoneId: $HostedZoneId"
echo "HostedZoneName: $HostedZoneName"
echo "ClusterName: $ClusterName"
#read -p "Press enter to continue or Ctl^c to exit: "
sleep 10

#12 Create DNS and Networking
echo '
[
  {
    "ParameterKey": "ClusterName",
    "ParameterValue": "'$ClusterName'"
  },
  {
    "ParameterKey": "InfrastructureName",
    "ParameterValue": "'$InfrastructureName'"
  },
  {
    "ParameterKey": "HostedZoneId",
    "ParameterValue": "'$HostedZoneId'"
  },
  {
    "ParameterKey": "HostedZoneName",
    "ParameterValue": "'$HostedZoneName'"
  },
  {
    "ParameterKey": "PublicSubnets",
    "ParameterValue": "'$PublicSubnetIds'"
  },
  {
    "ParameterKey": "PrivateSubnets",
    "ParameterValue": "'$PrivateSubnetIds'"
  },
  {
    "ParameterKey": "VpcId",
    "ParameterValue": "'$VpcId'"
  }
]
' > parameter-dns.json
echo "####################parameter-dns.json#################"      
cat parameter-dns.json
cp ../cf.network.yaml .
echo "################Creating DNS Stack##################"
aws cloudformation create-stack --stack-name $ClusterName-dns --template-body file://cf.network.yaml --parameters file://parameter-dns.json --capabilities CAPABILITY_NAMED_IAM
# wait for it to complete
while [ -n "$(aws cloudformation describe-stacks --stack-name $ClusterName-dns | awk '/CREATE_IN_PROGRESS/ {print $2}')" ]; do echo; date; \
aws cloudformation describe-stacks --stack-name $ClusterName-dns | awk '/CREATE_IN_PROGRESS/ {print $2}'; sleep 10; done;

#13 Create Security Stack
echo '
[
  {
    "ParameterKey": "InfrastructureName",
    "ParameterValue": "'$InfrastructureName'"
  },
  {
    "ParameterKey": "VpcCidr",
    "ParameterValue": "'$VpcCidr'"
  },
  {
    "ParameterKey": "PrivateSubnets",
    "ParameterValue": "'$PrivateSubnetIds'"
  },
  {
    "ParameterKey": "VpcId",
    "ParameterValue": "'$VpcId'"
  }
]
' > parameter-sec.json
echo "####################parameter-sec.json#################"      
cat parameter-sec.json
cp ../cf.sec_group.yaml .
echo "################Creating Security Stack##################"
aws cloudformation create-stack --stack-name $ClusterName-sec --template-body file://cf.sec_group.yaml --parameters file://parameter-sec.json --capabilities CAPABILITY_NAMED_IAM

while [ -n "$(aws cloudformation describe-stacks --stack-name $ClusterName-sec | awk '/CREATE_IN_PROGRESS/ {print $2}')" ]; do echo; date; \
aws cloudformation describe-stacks --stack-name $ClusterName-sec | awk '/CREATE_IN_PROGRESS/ {print $2}'; sleep 10; done;

#14 Copy Bootstrap config file
aws s3 mb s3://$ClusterName-infra
aws s3 cp bootstrap.ign s3://$ClusterName-infra/bootstrap.ign
echo "####################bootstrap.ign#################"      
aws s3 ls s3://$ClusterName-infra/bootstrap.ign
cat bootstrap.ign

#15 Create variables and assign values
`aws cloudformation describe-stacks --stack-name $ClusterName-dns | jq  -r '.Stacks[].Outputs[]  | "\(.OutputKey) \(.OutputValue)"' | awk '{print "export "$1"="$2}'`
`aws cloudformation describe-stacks --stack-name $ClusterName-sec | jq  -r '.Stacks[].Outputs[]  | "\(.OutputKey) \(.OutputValue)"' | awk '{print "export "$1"="$2}'` 
echo "############################"
echo ExternalApiTargetGroupArn: $ExternalApiTargetGroupArn
echo InternalApiTargetGroupArn: $InternalApiTargetGroupArn
echo ApiServerDnsName: $ApiServerDnsName
echo PrivateHostedZoneId: $PrivateHostedZoneId
echo InternalApiLoadBalancerName: $InternalApiLoadBalancerName
echo RegisterNlbIpTargetsLambda: $RegisterNlbIpTargetsLambda
echo InternalServiceTargetGroupArn: $InternalServiceTargetGroupArn
echo ExternalApiLoadBalancerName: $ExternalApiLoadBalancerName
echo MasterSecurityGroupId: $MasterSecurityGroupId
echo MasterInstanceProfile: $MasterInstanceProfile
echo WorkerSecurityGroupId: $WorkerSecurityGroupId
echo WorkerInstanceProfile: $WorkerInstanceProfile
echo "############################"
sleep 10
#read -p "Press enter to continue or Ctl^c to exit: "

#16 Create Bootstrap
echo '[
  {
    "ParameterKey": "InfrastructureName",
    "ParameterValue": "'$InfrastructureName'"
  },
  {
    "ParameterKey": "RhcosAmi",
    "ParameterValue": "'$RhcosAmi'"
  },
  {
    "ParameterKey": "AllowedBootstrapSshCidr",
    "ParameterValue": "0.0.0.0/0"
  },
  {
    "ParameterKey": "PublicSubnet",
    "ParameterValue": "'$PublicSubnetIds'"
  },
  {
    "ParameterKey": "MasterSecurityGroupId",
    "ParameterValue": "'$MasterSecurityGroupId'"
  },
  {
    "ParameterKey": "VpcId",
    "ParameterValue": "'$VpcId'"
  },
  {
    "ParameterKey": "BootstrapIgnitionLocation",
    "ParameterValue": "s3://'$ClusterName'-infra/bootstrap.ign"
  },
  {
    "ParameterKey": "AutoRegisterELB",
    "ParameterValue": "yes"
  },
  {
    "ParameterKey": "RegisterNlbIpTargetsLambdaArn",
    "ParameterValue": "'$RegisterNlbIpTargetsLambda'"
  },
  {
    "ParameterKey": "ExternalApiTargetGroupArn",
    "ParameterValue": "'$ExternalApiTargetGroupArn'"
  },
  {
    "ParameterKey": "InternalApiTargetGroupArn",
    "ParameterValue": "'$InternalApiTargetGroupArn'"
  },
  {
    "ParameterKey": "InternalServiceTargetGroupArn",
    "ParameterValue": "'$InternalServiceTargetGroupArn'"
  }
] ' > parameter-bootstrap.json
echo "####################parameter-bootstrap.json###################"      
cat parameter-bootstrap.json
cp ../cf.boot.yaml .
echo "############################"
echo "16.4 (For a multiple AZ install only): Clean up Parameter file to put the bootstrap server in one and only one AZ"
echo "I'll open the file for you to review/edit once you are ready..."
#read -p "Press enter to continue or Ctl^c to exit: "
#vi parameter-bootstrap.json
#read -p "Press enter to continue or Ctl^c to exit: "

echo "################Creating Bootstrap Stack##################"
aws cloudformation create-stack --stack-name $ClusterName-boot --template-body file://cf.boot.yaml --parameters file://parameter-bootstrap.json --capabilities CAPABILITY_NAMED_IAM

while [ -n "$(aws cloudformation describe-stacks --stack-name $ClusterName-boot | awk '/CREATE_IN_PROGRESS/ {print $2}')" ]; do echo; date; \
aws cloudformation describe-stacks --stack-name $ClusterName-boot | awk '/CREATE_IN_PROGRESS/ {print $2}'; sleep 10; done

aws cloudformation describe-stacks --stack-name $ClusterName-boot | jq  -r '.Stacks[].Outputs[]  | "\(.OutputKey) \(.OutputValue)"' | awk '{print "##########Hey Daniel - here is your bootstrap server: "$1"="$2}'
`aws cloudformation describe-stacks --stack-name $ClusterName-boot | jq  -r '.Stacks[].Outputs[]  | "\(.OutputKey) \(.OutputValue)"' | awk '{print "export "$1"="$2}'`
echo "##DJE Figure out what this variable is named and output it at the end."

#17 Create Control Plane Machines
echo "############################"
export cert=`cat master.ign | awk -F\" '{print $22}'`
echo "############################"
echo Cert: $cert
echo "############################"

echo '
[
  {
    "ParameterKey": "InfrastructureName",
    "ParameterValue": "'$InfrastructureName'"
  },
  {
    "ParameterKey": "RhcosAmi",
    "ParameterValue": "'$RhcosAmi'"
  },
  {
    "ParameterKey": "AutoRegisterDNS",
    "ParameterValue": "yes"
  },
  {
    "ParameterKey": "PrivateHostedZoneId",
    "ParameterValue": "'$PrivateHostedZoneId'"
  },
  {
    "ParameterKey": "PrivateHostedZoneName",
    "ParameterValue": "'$ClusterName.$HostedZoneName'"
  },
  {
    "ParameterKey": "Master0Subnet",
    "ParameterValue": "'$PrivateSubnetIds'"
  },
  {
    "ParameterKey": "Master1Subnet",
    "ParameterValue": "'$PrivateSubnetIds'"
  },
  {
    "ParameterKey": "Master2Subnet",
    "ParameterValue": "'$PrivateSubnetIds'"
  },
  {
    "ParameterKey": "MasterSecurityGroupId",
    "ParameterValue": "'$MasterSecurityGroupId'"
  },
  {
    "ParameterKey": "IgnitionLocation",
    "ParameterValue": "https://api-int.'$ClusterName.$HostedZoneName':22623/config/master"
  },
  {
    "ParameterKey": "CertificateAuthorities",
    "ParameterValue": "'$cert'"
  },
  {
    "ParameterKey": "MasterInstanceProfileName",
    "ParameterValue": "'$MasterInstanceProfile'"
  },
  {
    "ParameterKey": "MasterInstanceType",
    "ParameterValue": "m4.xlarge"
  },
  {
    "ParameterKey": "AutoRegisterELB",
    "ParameterValue": "yes"
  },
  {
    "ParameterKey": "RegisterNlbIpTargetsLambdaArn",
    "ParameterValue": "'$RegisterNlbIpTargetsLambda'"
  },
  {
    "ParameterKey": "ExternalApiTargetGroupArn",
    "ParameterValue": "'$ExternalApiTargetGroupArn'"
  },
  {
    "ParameterKey": "InternalApiTargetGroupArn",
    "ParameterValue": "'$InternalApiTargetGroupArn'"
  },
  {
    "ParameterKey": "InternalServiceTargetGroupArn",
    "ParameterValue": "'$InternalServiceTargetGroupArn'"
  }
]
' > parameter-cp.json
echo "####################parameter-cp.json###################"      
cat parameter-cp.json

cp ../cf.cp.yaml .
echo "############################"
echo "17.4 Multiple AZ Install only: Fix Master Subnets *(Skip this step for a single AZ install)"
echo "-Each Master Subnet should only have one subnet and if different AZ are used each one should be different"
echo "##Skip for a single AZ install##"
echo "############################"
#read -p "Ready to edit?"
#vi parameter-cp.json
#read -p "Press enter to continue or Ctl^c to exit: "
sleep 10
echo "################Creating Control Plane Stack##################"
aws cloudformation create-stack --stack-name $ClusterName-cp --template-body file://cf.cp.yaml --parameters file://parameter-cp.json --capabilities CAPABILITY_NAMED_IAM

while [ -n "$(aws cloudformation describe-stacks --stack-name $ClusterName-cp | awk '/CREATE_IN_PROGRESS/ {print $2}')" ]; do echo; date; \
aws cloudformation describe-stacks --stack-name $ClusterName-cp | awk '/CREATE_IN_PROGRESS/ {print $2}'; sleep 10; done

`aws cloudformation describe-stacks --stack-name $ClusterName-cp | jq  -r '.Stacks[].Outputs[]  | "\(.OutputKey) \(.OutputValue)"' | awk '{print "export "$1"="$2}'`

#18 Create Worker nodes
echo '
[
  {
    "ParameterKey": "InfrastructureName",
    "ParameterValue": "'$InfrastructureName'"
  },
  {
    "ParameterKey": "RhcosAmi",
    "ParameterValue": "ami-046fe691f52a953f9"
  },
  {
    "ParameterKey": "Subnet",
    "ParameterValue": "'$PrivateSubnetIds'"
  },
  {
    "ParameterKey": "WorkerSecurityGroupId",
    "ParameterValue": "'$WorkerSecurityGroupId'"
  },
  {
    "ParameterKey": "IgnitionLocation",
    "ParameterValue": "https://api-int.'$ClusterName.$HostedZoneName':22623/config/worker"
  },
  {
    "ParameterKey": "CertificateAuthorities",
    "ParameterValue": "'$cert'"
  },
  {
    "ParameterKey": "WorkerInstanceProfileName",
    "ParameterValue": "'$WorkerInstanceProfile'"
  },
  {
    "ParameterKey": "WorkerInstanceType",
    "ParameterValue": "m4.large"
  }
]' > parameter-worker.json
echo "####################parameter-worker.json###################"      
cat parameter-worker.json

cp ../cf.worker.yaml .
echo "################Creating Worker 1 Stack##################"
aws cloudformation create-stack --stack-name $ClusterName-worker1 --template-body file://cf.worker.yaml --parameters file://parameter-worker.json --capabilities CAPABILITY_NAMED_IAM
echo "################Creating Worker 2 Stack##################"
aws cloudformation create-stack --stack-name $ClusterName-worker2 --template-body file://cf.worker.yaml --parameters file://parameter-worker.json --capabilities CAPABILITY_NAMED_IAM
echo "################Creating Worker 3 Stack##################"
aws cloudformation create-stack --stack-name $ClusterName-worker3 --template-body file://cf.worker.yaml --parameters file://parameter-worker.json --capabilities CAPABILITY_NAMED_IAM

while [ -n "$(aws cloudformation describe-stacks --stack-name $ClusterName-worker1 | awk '/CREATE_IN_PROGRESS/ {print $2}')" ]; do echo; date; \
aws cloudformation describe-stacks --stack-name $ClusterName-worker1 | awk '/CREATE_IN_PROGRESS/ {print $2}'; sleep 10; done

# If you were going to do multiple workers, here is where you would do it.
#
# Final install command!
#read -p "Ready to start final install - Press enter to continue or Ctl^c to exit: "
echo "################Final Step: openshift-install##################"
sleep 10
echo "Started: ", `date`
../openshift-install --dir=. wait-for install-complete --log-level debug
echo "Finished: ", `date`
echo "############################"
