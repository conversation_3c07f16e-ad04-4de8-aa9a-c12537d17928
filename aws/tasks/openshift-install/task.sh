#!/bin/bash

set -eu

INIT_DIR=$( pwd )

cd install-hybrid-paas-repo/tasks/openshift-install/terraform

terraform init

#Ignition config file location.
IgnitionLocation="https://api-int.$CLUSTER_NAME.$DOMAIN:22623/config/master"

#Base64 encoded certificate authority string to use.
Ignition_CA_String="data:text/plain;charset=utf-8;base64,ABC...xYz=="

#The master security group ID to associate with master nodes.
Master_Security_Group_ID=$(cat terraform.tfstate | jq -r '.resources[] | select(.name=="master_sg") | .instances[].attributes.id')

#IAM profile to associate with master nodes.
Master_Instance_Profile_Name=TBD

#Load Balancer Automation
#Do you want to invoke NLB registration, which requires a Lambda ARN parameter?
Use_Provided_ELB_Automation=TBD

#ARN for NLB IP target registration lambda. Supply the value from the cluster infrastructure or select "no" for AutoRegisterELB.
RegisterNlbIpTargetsLambdaArn=TBD

#ARN for external API load balancer target group. Supply the value from the cluster infrastructure or select "no" for AutoRegisterELB.
ExternalApiTargetGroupArn=TBD

#ARN for internal API load balancer target group. Supply the value from the cluster infrastructure or select "no" for AutoRegisterELB.
InternalApiTargetGroupArn=TBD

#ARN for internal service load balancer target group. Supply the value from the cluster infrastructure or select "no" for AutoRegisterELB.
InternalServiceTargetGroupArn=TBD

#openshift-install create-ignition-file

terraform plan -compact-warnings \
  -state "$INIT_DIR"/terraform/terraform.tfstate \
  -var "prefix=${TERRAFORM_PREFIX}" \
  -var "aws_region=${aws_region}" \
  -var "aws_access_key_id=${aws_access_key_id}" \
  -var "aws_secret_access_key=${aws_secret_access_key}" \
  -var "aws_key_name=${aws_key_name}" \
  -var "vpc_id=${vpc_id}" \
  -out "$INIT_DIR"/terraform.tfplan

  terraform apply -compact-warnings \
  -state-out "$INIT_DIR"/terraform-state-output/terraform.tfstate \
  "$INIT_DIR"/terraform.tfplan