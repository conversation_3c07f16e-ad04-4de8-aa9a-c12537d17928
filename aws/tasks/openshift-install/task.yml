---
platform: linux
image_resource:
  type: docker-image
  source:
    repository: ((dtr_images_repo))
    ca_certs:
    - domain: ((dtr_repo_domain))
      cert: ((dtr_repo_cert))
    username: ((paas-eo-username))
    password: ((paas-eo-password))
    tag: latest

inputs:
- name: install-hybrid-paas-repo
- name: terraform-state

params:
  REGION:
  ENV:
  TERRAFORM_PREFIX:
  MASTER_INSTANCE_TYPE:
  RHCOSAMI:
  CLUSTER_NAME:
  DOMAIN_NAME:
  RHCOSAMI:

run:
  path: install-hybrid-paas-repo/aws/tasks/openshift-install/task.sh