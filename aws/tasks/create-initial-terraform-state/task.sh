#!/bin/bash

set -eu

echo -e "Checking for Terraform Output Bucket"
buckets=$(aws s3api list-buckets --query "Buckets[].Name")
set +e
echo $buckets | grep $TERRAFORM_PREFIX-pipeline
if [ "$?" -gt "0" ]; then
echo -e "Creating Terraform Output Bucket"
aws s3api create-bucket --bucket $TERRAFORM_PREFIX-pipeline --region $S3_REGION --create-bucket-configuration LocationConstraint=$S3_REGION
aws s3api put-bucket-versioning --bucket $TERRAFORM_PREFIX-pipeline --versioning-configuration Status=Enabled
else
echo -e "Terraform Output Bucket Found, skipping"
fi

echo -e "Checking for existing tfstate file"
files=$(aws s3 ls "${S3_BUCKET_TERRAFORM}/")
#files=$(aws --endpoint-url $S3_ENDPOINT --region $S3_REGION s3 ls "${S3_BUCKET_TERRAFORM}/")

set +e
echo $files | grep terraform.tfstate
if [ "$?" -gt "0" ]; then
  echo -e "Bootstrapping .tfstate file into bucket"
  echo "{\"version\": 3}" > terraform.tfstate
  aws s3 cp terraform.tfstate "s3://${S3_BUCKET_TERRAFORM}/terraform.tfstate"
  #aws s3 --endpoint-url $S3_ENDPOINT --region $S3_REGION cp terraform.tfstate "s3://${S3_BUCKET_TERRAFORM}/terraform.tfstate"
  set +x
  if [ "$?" -gt "0" ]; then
    echo "Failed to upload empty tfstate file"
    exit 1
  fi
else
  echo "terraform.tfstate file found, skipping"
  exit 0
fi
