---
platform: linux
image_resource:
  type: docker-image
  source:
    repository: ((dtr_images_repo))
    ca_certs:
    - domain: ((dtr_repo_domain))
      cert: ((dtr_repo_cert))
    username: ((paas-eo-username))
    password: ((paas-eo-password))
    tag: latest

inputs:
- name: install-hybrid-paas-repo

params:
  TERRAFORM_PREFIX:
  S3_BUCKET_TERRAFORM:
  S3_ENDPOINT:
  S3_REGION:
  AWS_ACCESS_KEY_ID:
  AWS_SECRET_ACCESS_KEY:

run:
  path: install-hybrid-paas-repo/aws/tasks/create-initial-terraform-state/task.sh
