# For terraform state file (http://docs.aws.amazon.com/general/latest/gr/rande.html#s3_region)
#S3_ENDPOINT: https://s3-fips.us-east-1.amazonaws.com
S3_ENDPOINT: https://s3.us-west-1.amazonaws.com
S3_OUTPUT_BUCKET: hp-upw1-prd-pipeline
aws_region: us-west-1
env: prd
terraform_prefix: hp-upw1-prd
iam_region: aws # <-- aws-us-gov or aws
s3_region: "aws:s3"
common_deny_arn: arn:aws:iam::852024532004:policy/CommonDeny

# AMI to use for nat instances
amis_nat: ami-01cd9be80fcd58eb1
# AMI to use for bastion host
bastion_ami: ami-0d821453063a3c9b1
aws_key_name: ocp-hpw1-prd
bastion_ip_az1: ************* # <--- enter bastion IP, use the base IP +10
nat_ip_az1: ************* # <--- enter NatAz1 IP use the Base IP +20
nat_ip_az2: ************* # <--- enter NatAz2 IP use the base IP +20
nat_ip_az3: "" # <--- enter NatAz3 IP

vpc_id: vpc-00137f3c0ed618ab7
vpc_cidr: ************/26
network_acl_id: acl-0b6b671b7f9303133
aws_az1: us-west-1a
aws_az2: us-west-1c
aws_az3: ""
LMI_subnet_az1_id: subnet-00eba7255675577c4
LMI_subnet_az2_id: subnet-0661369db6463b8bd
LMI_subnet_az3_id:
private_subnet_cidr_az1: **********/20
private_subnet_cidr_az2: ***********/20
private_subnet_cidr_az3: ""

master_instance_type: m5.xlarge
cluster_name: upw1-prd
domain_name: global.lmco.com
