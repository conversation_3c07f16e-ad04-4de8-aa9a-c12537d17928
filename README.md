# hybrid-paas-pipeline

###  This is the repository that contains all of the pipeline files and scripts that create and update the PaaS for Hybrid PaaS.

A bash script that calls Terraform is used to create all of the AWS infrastructure as code.

OpenShift 4.3 is deployed using a combination of Terraform, OpenShift-Install binary, and CoreOS Ignition files.

![N|Pipeline](img/pipeline.png)

## Environment Naming (Environment Prefix)
```diff
+ Service Identifier (Sequence 1,2)

    HP = Hybrid PaaS
```
```diff
+ Region (Sequence 3,4,5,6)

    UPE1 = AWS Commercial US-EAST-1
    UPE2 = AWS Commercial US-EAST-2
    UPW1 = AWS Commercial US-WEST-1
    UPW2 = AWS Commercial US-WEST-2
    UGE1 = AWS GovCloud US-GOV-EAST-1
    UGW1 = AWS GovCloud US-GOV-WEST-1
    UVF1 = US Valley Forge (Private East)
    UDD1 = Denver Data Center (Private West)
    USV1 = Switch Las Vegas (Co-Lo)
```

```diff
+ Environment Identifier (Sequence 6,7,8)

    TST = Test (Platform Sandbox/Prototyping)
    DEV = Development
    PRD = Production/Staging
```